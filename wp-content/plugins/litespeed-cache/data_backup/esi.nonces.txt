## To predefine more list, please submit a PR to https://github.com/litespeedtech/lscache_wp/blob/dev/data/esi.nonces.txt
## 	 Comment Format:
## 		1. `# this is comment`
## 		2. `##this is comment`


## Predefined elsewhere so not needed here:

## WordPress core
# stats_nonce
# subscribe_nonce

# Divi Theme Builder
# et-pb-contact-form-submit
# et_frontend_nonce
# et_ab_log_nonce

# WooCommerce PayPal Checkout
# _wc_ppec_update_shipping_costs_nonce private
# _wc_ppec_start_checkout_nonce private
# _wc_ppec_generate_cart_nonce private

# User Switching
# switch_to_olduser_'<ID>'

# Caldera Forms
# caldera_forms_front_*


## Predefined list of ESI nonces:

# WordPress REST nonce
wp_rest

# CM Registration Pro
cmreg_registration_nonce private
role_nonce private

# WooCommerce Delivery Area Pro #16843635
wdap-call-nonce private

# SEOpress Cookie Consent
seopress_cookies_user_consent_nonce

# SearchWP Metrics
swpmtxnonce

# The Events Calendar
_tec_view_rest_nonce_primary
_tec_view_rest_nonce_secondary

# wpDataTables #986128
wdt*

# WPBakery gallery
_vcnonce
data-vc-public-nonce

# Extra Theme
rating_nonce
timeline_nonce
blog_feed_nonce

# WS Form
wsf_post

# Easy Digital Download (EDD)
edd-* private
edd_* private

# WP Menu Cart
wpmenucart private

# Advanced Custom Fields + Advanced Forms
acf_nonce
af_form_nonce
af_submission_*

# Woo nonce
woocommerce-login

# Premium Addons for Elementor
pa-blog-widget-nonce

# WPUF User Frontend
wpuf* private

# MetForm
form_nonce

# Mobile hamburger menu - jetMenu #306983 #163710 PR#419
tgmpa-*
bulk-*

# WP Data Access
wpda-*

# Elementor
elementor-pro-frontend
elementor-conversion-center-click